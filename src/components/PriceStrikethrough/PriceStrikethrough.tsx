import React from 'react';
import styled from '@emotion/styled';
import { themeGet, space } from 'styled-system';
import { css } from '@emotion/core';
import { FormatNumber } from 'components/formatters';
import { Decimal } from 'decimal.js';
import { PAYMENT_CURRENCIES } from 'config/constants';

export interface PriceData {
  currency: string | null;
  amount: string | null; // Runtime accepts string | number, but prefer string for type safety
}

export interface PriceProps {
  price?: PriceData;
  asSup?: boolean;
  withDecimal?: boolean;
  mt?: number;
  ml?: number;
  mr?: number;
}

const StyledPrice = styled.span<{ asSup?: boolean }>`
  text-decoration: line-through;
  font-size: ${themeGet('fontSizes.sm')};
  margin-right: ${themeGet('space.1')};
  color: ${themeGet('colors.greys.charcoal')};
  line-height: ${themeGet('lineHeights.normal')};
  ${space}

  ${(props) =>
    props.asSup &&
    css`
      vertical-align: super;
      position: relative;
      top: ${themeGet('space.2')};
    `};
`;

const ScreenReaderOnly = styled.span`
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
`;

const PriceStrikethrough = ({ price, asSup = false, withDecimal = false, ...rest }: PriceProps) => {
  // Early return if no valid price data
  if (!price?.amount || price.amount === '0') return null;

  const { amount, currency } = price;

  try {
    const decimalAmount = new Decimal(amount);
    const isCash = currency === PAYMENT_CURRENCIES.CASH;
    const decimalPlaces = isCash && withDecimal ? 2 : 0;
    const formattedAmount = FormatNumber({ number: decimalAmount, decimalPlaces });

    return (
      <StyledPrice asSup={asSup} {...rest}>
        {isCash && '$'}
        {formattedAmount}
        {!isCash && ` ${PAYMENT_CURRENCIES.POINTS}`}
        <ScreenReaderOnly>
          {' '}
          Original price was {isCash ? '$' : ''}
          {formattedAmount}
          {!isCash ? ` ${PAYMENT_CURRENCIES.POINTS}` : ''}{' '}
        </ScreenReaderOnly>
      </StyledPrice>
    );
  } catch (error) {
    // Log error and return null for invalid amounts
    // eslint-disable-next-line no-console
    console.warn('PriceStrikethrough: Invalid price amount', { amount, currency, error });
    return null;
  }
};

export default PriceStrikethrough;
