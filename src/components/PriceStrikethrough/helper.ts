import { PAYMENT_METHODS } from 'lib/enums/payment';
import { PAYMENT_CURRENCIES } from 'config/constants';

// Type definitions for better type safety
export interface PriceStrikethroughData {
  amount: string | number;
  currency: string;
}

export interface ChargesType {
  payableAtBooking?: {
    total?: {
      pointsStrikethrough?: number;
      points?: number;
    };
  };
  strikethrough?: {
    price?: PriceStrikethroughData;
  };
}

export const getQuotePriceStrikethrough = (charges: ChargesType | null | undefined, payWith: string): PriceStrikethroughData | null => {
  if (!charges) return null;

  if (payWith === PAYMENT_METHODS.CASH) {
    return charges.strikethrough?.price || null;
  } else {
    const pointsStrikethrough = charges.payableAtBooking?.total?.pointsStrikethrough;
    return pointsStrikethrough
      ? {
          amount: pointsStrikethrough,
          currency: PAYMENT_CURRENCIES.POINTS,
        }
      : null;
  }
};
